import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules, ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { IProductModuleService, IPricingModuleService } from "@camped-ai/framework/types";
import { format, parseISO } from "date-fns";
import { createPriceListsWorkflow } from "@camped-ai/medusa/core-flows";

/**
 * GET endpoint to retrieve product variant pricing data for calendar view
 * Fetches date-based pricing from price lists and inventory availability
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const variantId = req.params.id;
    const { start_date, end_date, currency_code = "GBP" } = req.query as {
      start_date?: string;
      end_date?: string;
      currency_code?: string;
    };

    console.log("Fetching pricing for variant:", variantId, "from", start_date, "to", end_date);

    const productModuleService = req.scope.resolve(Modules.PRODUCT) as IProductModuleService;
    const pricingModuleService = req.scope.resolve(Modules.PRICING) as IPricingModuleService;
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Get the product variant
    
   

 const {data:variant} = await query.graph({
  entity: "product_variant",
  fields: ["*", "price_set.*"],
  filters: {
    id: [variantId],
  },
});

const price_set_id=variant?.[0]?.price_set?.id 

     

    console.log({price_set_id})

     const {data:prices} = await query.graph({
  entity: "price",
  fields: ["*", "price_list.*"],
  filters: {
    price_set: {
      id: [price_set_id],
    },
    price_list: {
      starts_at: { $gte: start_date },
      ends_at: { $lte: end_date },
      status: "active",
    }
  },
});
console.log({prices})

   

   


    // Process prices to create date-based pricing
    const dateBasedPricing: Array<{
      date: string;
      price_list_id?: string;
      price_list_name?: string;
      amount: number;
      currency_code: string;
      is_base_price: boolean;
      cost?: number;
      margin?: number;
      selling_price?: number;
    }> = [];

    // Process each price and extract date from price list
    if (prices && prices.length > 0) {
      prices.forEach((price: any) => {
        if (price.price_list && price.price_list.starts_at) {
          // Extract date from price list start date (YYYY-MM-DD format)
          const startDate = new Date(price.price_list.starts_at);
          const dateString = format(startDate, 'yyyy-MM-dd');

          dateBasedPricing.push({
            date: dateString,
            price_list_id: price.price_list_id,
            price_list_name: price.price_list.title || price.price_list.name,
            amount: price.amount || 0,
            currency_code: price.currency_code,
            is_base_price: false,
            cost: Number(variant?.[0]?.metadata?.cost) || 0,
            margin: Number(variant?.[0]?.metadata?.selling_margin) || 0,
            selling_price: price.amount || 0,
          });
        }
      });
    }

    // Sort by date
    dateBasedPricing.sort((a, b) => a.date.localeCompare(b.date));


    
    // Get inventory date levels for availability
    let inventoryData: any[] = [];
    try {
      const { data: inventoryDateLevels } = await query.graph({
        entity: "inventory_date_level",
        filters: {
          product_variant_id: variantId,
          ...(start_date && end_date && {
            from_date: { $gte: start_date },
            to_date: { $lte: end_date },
          }),
        },
        fields: [
          "id",
          "from_date",
          "to_date",
          "available_quantity",
          "status",
          "inventory_item_id",
        ],
      });

      inventoryData = inventoryDateLevels || [];
    } catch (error) {
      console.warn("Could not fetch inventory date levels:", error);
      // Continue without inventory data
    }

    return res.status(200).json({
      variant: {
        id: variant?.[0]?.id,
        title: variant?.[0]?.title,
        sku: variant?.[0]?.sku,
        product_id: variant?.[0]?.product_id,
        product_title: variant?.[0]?.product?.title,
        metadata: variant?.[0]?.metadata,
      },
    
      date_based_pricing: dateBasedPricing,
      inventory_availability: inventoryData,
      currency_code,
      date_range: {
        start_date,
        end_date,
      },
    });
  } catch (error) {
    console.error("Error fetching variant pricing:", error);
    return res.status(500).json({
      message: "Failed to fetch variant pricing",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * PUT endpoint to update variant pricing for specific dates using workflow
 */
export const PUT = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const variantId = req.params.id;
    const {
      date,
      amount,
      currency_code = "GBP",
      cost,
      margin,
      price_list_id,
      cost_currency,
      gross_cost,
      commission,
      net_cost,
      margin_rate,
      selling_price_cost_currency
    } = req.body as {
      date: string;
      amount: number;
      currency_code?: string;
      cost?: number;
      margin?: number;
      price_list_id?: string;
      cost_currency?: string;
      gross_cost?: number;
      commission?: number;
      net_cost?: number;
      margin_rate?: number;
      selling_price_cost_currency?: number;
    };

    if (!date || !amount) {
      return res.status(400).json({
        message: "Date and amount are required",
      });
    }

    console.log("Updating pricing for variant:", variantId, "date:", date, "amount:", amount, "price_list_id:", price_list_id);

    const productModuleService = req.scope.resolve(Modules.PRODUCT) as IProductModuleService;
    const pricingModuleService = req.scope.resolve(Modules.PRICING) as IPricingModuleService;
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Verify variant exists
    let variant: any;
    try {
      variant = await productModuleService.retrieveProductVariant(variantId);
    } catch (error) {
      return res.status(404).json({ message: "Product variant not found" });
    }

    // Check if price_list_id is provided (existing price) or we need to find/create one
    let finalPriceListId = price_list_id;
    let isUpdate = false;

    if (price_list_id) {
      // Update existing price list by recreating it with new price
      try {
        console.log("Updating existing price list:", price_list_id);

        // Get the existing price list details
        const { data: existingPriceList } = await query.graph({
          entity: "price_list",
          filters: {
            id: [price_list_id],
          },
          fields: ["id", "title", "description", "starts_at", "ends_at", "status"],
        });

        if (existingPriceList && existingPriceList.length > 0) {
          const priceList = existingPriceList[0];

          // Make the old price list inactive
          await pricingModuleService.updatePriceLists([{
            id: price_list_id,
            status: "draft", // Use "draft" instead of "inactive"
          }]);

          console.log("Made old price list inactive:", price_list_id);

          // Create new price list with updated price
          const { result } = await createPriceListsWorkflow(req.scope).run({
            input: {
              price_lists_data: [{
                title: priceList.title,
                description: priceList.description,
                status: "active",
                starts_at: priceList.starts_at,
                ends_at: priceList.ends_at,
                prices: [{
                  amount: amount,
                  currency_code: currency_code,
                  variant_id: variantId,
                }],
              }]
            }
          });

          finalPriceListId = result?.[0]?.id;
          isUpdate = true;
          console.log("Created new price list with updated price:", finalPriceListId);
        } else {
          console.log("Price list not found, will create new one");
          finalPriceListId = null;
        }
      } catch (updateError) {
        console.error("Failed to update existing price list:", updateError);
        // Fall back to creating new price list
        finalPriceListId = null;
      }
    }

    // If no price_list_id provided or update failed, create new price list
    if (!finalPriceListId || !isUpdate) {
      try {
        console.log("Creating new price list for date:", date);

        const { result } = await createPriceListsWorkflow(req.scope).run({
          input: {
            price_lists_data: [{
              title: `${variant.title} - ${date}`,
              description: `Daily pricing for ${date}`,
              status: "active",
              starts_at: date + "T00:00:00Z",
              ends_at: date + "T23:59:59Z",
              prices: [{
                amount: amount,
                currency_code: currency_code,
                variant_id: variantId,
              }],
            }]
          }
        });

        finalPriceListId = result?.[0]?.id;
        console.log("Created new price list:", finalPriceListId);
      } catch (priceListError) {
        console.error(`Failed to create price list for ${date}:`, priceListError);
        throw priceListError;
      }
    }

    // Update variant metadata with cost, margin, and new pricing fields if provided
    if (cost !== undefined || margin !== undefined || cost_currency !== undefined ||
        gross_cost !== undefined || commission !== undefined || net_cost !== undefined ||
        margin_rate !== undefined || selling_price_cost_currency !== undefined) {
      try {
        const updatedMetadata = {
          ...variant.metadata,
          ...(cost !== undefined && { cost }),
          ...(margin !== undefined && { selling_margin: margin }),
          ...(cost_currency !== undefined && { cost_currency }),
          ...(gross_cost !== undefined && { gross_cost }),
          ...(commission !== undefined && { commission }),
          ...(net_cost !== undefined && { net_cost }),
          ...(margin_rate !== undefined && { margin_rate }),
          ...(selling_price_cost_currency !== undefined && { selling_price_cost_currency }),
        };

        await productModuleService.updateProductVariants(variantId, {
          metadata: updatedMetadata,
        });
      } catch (error) {
        console.warn("Could not update variant metadata:", error);
        // Don't fail the request if metadata update fails
      }
    }

    return res.status(200).json({
      message: isUpdate ? "Pricing updated successfully" : "Pricing created successfully",
      price_list_id: finalPriceListId,
      date,
      amount,
      currency_code,
      cost,
      margin,
      cost_currency,
      gross_cost,
      commission,
      net_cost,
      margin_rate,
      selling_price_cost_currency,
      is_update: isUpdate,
    });
  } catch (error) {
    console.error("Error updating variant pricing:", error);
    return res.status(500).json({
      message: "Failed to update variant pricing",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
