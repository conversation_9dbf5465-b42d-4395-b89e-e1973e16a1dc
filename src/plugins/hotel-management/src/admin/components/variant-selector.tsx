import React, { useState, useMemo } from "react";
import {
  Select,
  Input,
  Button,
  Text,
  Badge,
} from "@camped-ai/ui";
import { Search, Package, Tag } from "lucide-react";
import { useVariantsForPricing } from "../hooks/use-variant-pricing-calendar";

export interface ProductVariant {
  id: string;
  title: string;
  sku: string;
  product_id: string;
  product?: {
    id: string;
    title: string;
    handle: string;
  };
  metadata?: Record<string, any>;
  inventory_quantity?: number;
  manage_inventory?: boolean;
}

type VariantSelectorProps = {
  selectedVariantId?: string;
  onVariantSelect: (variant: ProductVariant) => void;
  productId?: string;
  className?: string;
};

const VariantSelector: React.FC<VariantSelectorProps> = ({
  selectedVariantId,
  onVariantSelect,
  productId,
  className = "",
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProductFilter, setSelectedProductFilter] = useState<string>("");

  // Fetch variants
  const { data: variants = [], isLoading, error } = useVariantsForPricing(productId);

  // Filter and search variants
  const filteredVariants = useMemo(() => {
    if (!variants) return [];

    return variants.filter((variant: ProductVariant) => {
      // Search filter
      const matchesSearch = !searchTerm || 
        variant.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        variant.sku?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        variant.product?.title?.toLowerCase().includes(searchTerm.toLowerCase());

      // Product filter
      const matchesProduct = !selectedProductFilter || 
        variant.product_id === selectedProductFilter;

      return matchesSearch && matchesProduct;
    });
  }, [variants, searchTerm, selectedProductFilter]);

  // Get unique products for filter
  const products = useMemo(() => {
    if (!variants) return [];
    
    const productMap = new Map();
    variants.forEach((variant: ProductVariant) => {
      if (variant.product && !productMap.has(variant.product.id)) {
        productMap.set(variant.product.id, variant.product);
      }
    });
    
    return Array.from(productMap.values());
  }, [variants]);

  // Get selected variant details
  const selectedVariant = variants?.find((v: ProductVariant) => v.id === selectedVariantId);

  if (error) {
    return (
      <div className={`p-4 border border-red-200 rounded-lg bg-red-50 ${className}`}>
        <Text className="text-red-600">Failed to load variants</Text>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search and Filters */}
      <div className="flex gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search variants by name, SKU, or product..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        {!productId && products.length > 0 && (
          <Select
            value={selectedProductFilter}
            onValueChange={setSelectedProductFilter}
          >
            <option value="">All Products</option>
            {products.map((product: any) => (
              <option key={product.id} value={product.id}>
                {product.title}
              </option>
            ))}
          </Select>
        )}
      </div>

      {/* Selected Variant Display */}
      {selectedVariant && (
        <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200">
          <div className="flex items-center gap-3">
            <Package className="h-5 w-5 text-blue-600" />
            <div className="flex-1">
              <Text className="font-medium">{selectedVariant.title}</Text>
              <Text className="text-sm text-muted-foreground">
                {selectedVariant.product?.title} • SKU: {selectedVariant.sku}
              </Text>
            </div>
            <Badge variant="secondary">Selected</Badge>
          </div>
        </div>
      )}

      {/* Variants List */}
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        ) : filteredVariants.length === 0 ? (
          <div className="text-center py-8">
            <Package className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <Text className="text-muted-foreground">
              {searchTerm || selectedProductFilter ? "No variants match your search" : "No variants available"}
            </Text>
          </div>
        ) : (
          filteredVariants.map((variant: ProductVariant) => (
            <div
              key={variant.id}
              className={`
                p-3 border rounded-lg cursor-pointer transition-colors
                hover:bg-muted/50
                ${variant.id === selectedVariantId 
                  ? "border-blue-500 bg-blue-50 dark:bg-blue-950/20" 
                  : "border-border"
                }
              `}
              onClick={() => onVariantSelect(variant)}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Text className="font-medium">{variant.title}</Text>
                    {variant.metadata?.add_on_service && (
                      <Badge variant="outline">
                        <Tag className="h-3 w-3 mr-1" />
                        Service
                      </Badge>
                    )}
                    {variant.metadata?.status && (
                      <Badge
                        variant={variant.metadata.status === 'active' ? 'green' : 'red'}
                      >
                        {variant.metadata.status}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-4 mt-1">
                    <Text className="text-sm text-muted-foreground">
                      {variant.product?.title}
                    </Text>
                    {variant.sku && (
                      <Text className="text-sm text-muted-foreground">
                        SKU: {variant.sku}
                      </Text>
                    )}
                  </div>

                  {/* Metadata info */}
                  {variant.metadata && (
                    <div className="flex items-center gap-4 mt-2">
                      {variant.metadata.cost && (
                        <Text className="text-xs text-muted-foreground">
                          Cost: {variant.metadata.cost_currency || 'GBP'} {variant.metadata.cost}
                        </Text>
                      )}
                      {variant.metadata.selling_margin && (
                        <Text className="text-xs text-muted-foreground">
                          Margin: {(variant.metadata.selling_margin * 100).toFixed(1)}%
                        </Text>
                      )}
                      {variant.metadata.service_level && (
                        <Badge variant="outline">
                          {variant.metadata.service_level}
                        </Badge>
                      )}
                      {variant.metadata.category && (
                        <Badge variant="outline">
                          {variant.metadata.category}
                        </Badge>
                      )}
                      {variant.metadata.unit_type && (
                        <Text className="text-xs text-muted-foreground">
                          {variant.metadata.unit_type}
                        </Text>
                      )}
                    </div>
                  )}
                </div>

                {/* Inventory info */}
                {variant.manage_inventory && (
                  <div className="text-right">
                    <Text className="text-sm font-medium">
                      {variant.inventory_quantity || 0}
                    </Text>
                    <Text className="text-xs text-muted-foreground">
                      in stock
                    </Text>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Summary */}
      {!isLoading && (
        <div className="flex items-center justify-between text-sm text-muted-foreground pt-2 border-t">
          <Text>
            {filteredVariants.length} of {variants?.length || 0} variants
          </Text>
          {searchTerm && (
            <Button
              variant="transparent"
              size="small"
              onClick={() => setSearchTerm("")}
            >
              Clear search
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default VariantSelector;
