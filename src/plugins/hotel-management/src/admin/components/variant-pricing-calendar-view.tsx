import React, { useState, useMemo } from "react";
import {
  Heading,
  Text,
  Button,
  IconButton,
  Input,
  Select,
  toast,
} from "@camped-ai/ui";
import {
  ChevronLeft,
  ChevronRight,
  Edit,
  X,
  Calendar,
} from "lucide-react";
import {
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  addMonths,
  subMonths,
  isSameMonth,
  isToday,
  startOfWeek,
  endOfWeek,
} from "date-fns";
import {
  useVariantPricingCalendar,
  useUpdateVariantPricing,
  getPricingForDate,
  getAvailabilityForDate,
} from "../hooks/use-variant-pricing-calendar";
import { useQuery } from "@tanstack/react-query";
import {
  convertToCents,
  convertFromCents,
  type DailyVariantPricingInfo,
} from "../../utils/variant-pricing-calendar-utils";

// Currency types for the API response
interface StoreCurrency {
  currency_code: string;
  is_default?: boolean;
}

interface Store {
  id: string;
  supported_currencies: StoreCurrency[];
}

interface StoreResponse {
  stores: Store[];
}



interface CurrencyOption {
  value: string;
  label: string;
  symbol: string;
  decimal_digits: number;
  is_default?: boolean;
}

type VariantPricingCalendarViewProps = {
  variantId: string;
  currencyCode: string;
  onPriceUpdate?: (
    date: Date,
    price: number,
    costMarginData?: {
      cost: number;
      margin: number;
    }
  ) => Promise<void>;
  onRefetch?: () => void;
  canEdit?: boolean;
};

const VariantPricingCalendarView: React.FC<VariantPricingCalendarViewProps> = ({
  variantId,
  currencyCode,
  onPriceUpdate: _onPriceUpdate,
  onRefetch,
  canEdit = false,
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedDates, setSelectedDates] = useState<Set<string>>(new Set());
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [editingPrice, setEditingPrice] = useState<string>("");
  const [editingCost, setEditingCost] = useState<string>("");
  const [editingMargin, setEditingMargin] = useState<string>("");
  const [editingCostCurrency, setEditingCostCurrency] = useState<string>("");
  const [editingGrossCost, setEditingGrossCost] = useState<string>("");
  const [editingCommission, setEditingCommission] = useState<string>("");
  const [editingNetCost, setEditingNetCost] = useState<string>("");
  const [editingMarginRate, setEditingMarginRate] = useState<string>("");
  const [editingSellingPriceCostCurrency, setEditingSellingPriceCostCurrency] = useState<string>("");
  const [isMultiSelectMode, setIsMultiSelectMode] = useState(false);
  const [hoverDate, setHoverDate] = useState<Date | null>(null);

  // Fetch store's configured currencies
  const { data: storeCurrencies, isLoading: currenciesLoading } = useQuery<CurrencyOption[]>({
    queryKey: ["store-currencies"],
    queryFn: async (): Promise<CurrencyOption[]> => {
      try {
        // Get the store's configured currencies
        const storeResponse = await fetch("/admin/stores", {
          credentials: 'include',
        });

        if (!storeResponse.ok) {
          throw new Error("Failed to fetch store");
        }

        const storeData: StoreResponse = await storeResponse.json();
        const store = storeData?.stores?.[0];

        if (!store || !store.supported_currencies?.length) {
          // Return emergency fallback if no store currencies configured
          return [{
            value: "USD",
            label: "USD",
            symbol: "USD",
            decimal_digits: 2,
          }];
        }

        // Transform store currencies to options format
        const storeCurrencyOptions = store.supported_currencies.map((storeCurrency: StoreCurrency) => {
          const currencyCode = storeCurrency.currency_code.toUpperCase();

          return {
            value: currencyCode,
            label: currencyCode,
            symbol: currencyCode,
            decimal_digits: 2, // Default to 2 decimal places
            is_default: storeCurrency.is_default,
          };
        });

        return storeCurrencyOptions;
      } catch (error) {
        console.error("Failed to fetch store currencies:", error);
        // Return emergency fallback on error
        return [{
          value: "USD",
          label: "USD",
          symbol: "USD",
          decimal_digits: 2,
        }];
      }
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });

  const currencyOptions = storeCurrencies || [];

  // Get default currency (marked as default or first one)
  const defaultCurrency = currencyOptions.find((c: CurrencyOption) => c.is_default) || currencyOptions[0];

  // Calculate calendar date range
  const calendarStart = startOfWeek(startOfMonth(currentDate), { weekStartsOn: 1 });
  const calendarEnd = endOfWeek(endOfMonth(currentDate), { weekStartsOn: 1 });
  const calendarDays = eachDayOfInterval({ start: calendarStart, end: calendarEnd });

  // Fetch pricing data for the calendar date range
  const { data: pricingData, refetch: refetchPricingData, isLoading } = useVariantPricingCalendar({
    variantId,
    startDate: calendarStart,
    endDate: calendarEnd,
    currencyCode,
    enabled: !!variantId,
  });

  // Update pricing mutation
  const updatePricingMutation = useUpdateVariantPricing();

  // Calculate monthly pricing info
  const monthlyPricingInfo = useMemo(() => {
    if (!pricingData) return new Map<string, DailyVariantPricingInfo>();

    const pricingMap = new Map<string, DailyVariantPricingInfo>();

    calendarDays.forEach(date => {
      const dateKey = format(date, 'yyyy-MM-dd');
      
      // Get pricing for this date
      const pricing = getPricingForDate(date, pricingData);
      console.log(pricing)
      const availability = getAvailabilityForDate(date, pricingData.inventory_availability);
      
      pricingMap.set(dateKey, {
        date,
        cost: pricingData.variant.metadata?.cost || null,
        margin: pricingData.variant.metadata?.selling_margin || null,
        selling_price: pricing?.selling_price || 0,
        isPriceListOverride: pricing ? !pricing.is_base_price : false,
        priceListName: pricing?.price_list_name,
        priceListId: pricing?.price_list_id,
        isAvailable: availability.is_available,
        availableQuantity: availability.available_quantity,
        inventoryStatus: availability.status,
      });
    });

    return pricingMap;
  }, [calendarDays, pricingData]);

  // Navigation handlers
  const handlePreviousMonth = () => {
    setCurrentDate(prev => subMonths(prev, 1));
  };

  const handleNextMonth = () => {
    setCurrentDate(prev => addMonths(prev, 1));
  };

  const handleToday = () => {
    setCurrentDate(new Date());
  };

  // Helper functions
  const getCurrencySymbol = () => {
    const symbols: Record<string, string> = {
      'GBP': '£',
      'USD': '$',
      'EUR': '€',
      'CHF': 'CHF',
      'JPY': '¥',
    };
    return symbols[currencyCode] || currencyCode;
  };

  const formatCurrency = (amount: number) => {
    return (amount / 100).toFixed(2);
  };

  // Sidebar handlers
  const handleSidebarClose = () => {
    setSidebarOpen(false);
    setSelectedDate(null);
    setSelectedDates(new Set());
    setIsMultiSelectMode(false);
    // Clear all editing fields
    setEditingPrice("");
    setEditingCost("");
    setEditingMargin("");
    setEditingCostCurrency("");
    setEditingGrossCost("");
    setEditingCommission("");
    setEditingNetCost("");
    setEditingMarginRate("");
    setEditingSellingPriceCostCurrency("");
  };

  const handleSidebarForceClose = () => {
    handleSidebarClose();
  };

  // Date selection handlers
  const handleDateClick = (date: Date, event?: React.MouseEvent) => {
    if (!canEdit) return;

    const dateKey = format(date, 'yyyy-MM-dd');
    const pricingInfo = monthlyPricingInfo.get(dateKey);

    if (!pricingInfo) return;

    // Check if Ctrl/Cmd key is held for multi-date selection
    if (event?.ctrlKey || event?.metaKey) {
      event.preventDefault();
      event.stopPropagation();

      const newSelectedDates = new Set(selectedDates);
      if (newSelectedDates.has(dateKey)) {
        newSelectedDates.delete(dateKey);
      } else {
        newSelectedDates.add(dateKey);
      }
      setSelectedDates(newSelectedDates);
      setSelectedDate(null);
      setIsMultiSelectMode(true);
      setSidebarOpen(true);
    } else {
      // Single date selection
      setSelectedDate(date);
      setSelectedDates(new Set());
      setIsMultiSelectMode(false);
      setSidebarOpen(true);

      // Pre-populate edit fields
      setEditingPrice(convertFromCents(pricingInfo.selling_price).toString());
      setEditingCost(pricingInfo.cost?.toString() || "");
      setEditingMargin(pricingInfo.margin?.toString() || "");

      // Pre-populate new pricing fields from metadata
      const metadata = pricingData?.variant?.metadata || {};
      setEditingCostCurrency(pricingInfo.currency_code || defaultCurrency?.value || currencyCode);
      setEditingGrossCost(metadata.gross_cost?.toString() || "");
      setEditingCommission(metadata.commission?.toString() || "");
      setEditingNetCost(metadata.net_cost?.toString() || "");
      setEditingMarginRate(metadata.margin_rate?.toString() || "");
      setEditingSellingPriceCostCurrency(convertFromCents(pricingInfo.selling_price).toString());
    }
  };

  // Price update handler
  const handlePriceUpdate = async () => {
    if (!selectedDate && selectedDates.size === 0) {
      toast.error("Please select a date to update");
      return;
    }

    const price = parseFloat(editingPrice);
    const cost = editingCost ? parseFloat(editingCost) : undefined;
    const margin = editingMargin ? parseFloat(editingMargin) : undefined;

    // Parse new pricing fields
    const costCurrency = editingCostCurrency || defaultCurrency?.value || currencyCode;
    const grossCost = editingGrossCost ? parseFloat(editingGrossCost) : undefined;
    const commission = editingCommission ? parseFloat(editingCommission) : undefined;
    const netCost = editingNetCost ? parseFloat(editingNetCost) : undefined;
    const marginRate = editingMarginRate ? parseFloat(editingMarginRate) : undefined;
    const sellingPriceCostCurrency = editingSellingPriceCostCurrency ? parseFloat(editingSellingPriceCostCurrency) : undefined;

    if (isNaN(price) || price < 0) {
      toast.error("Please enter a valid selling price");
      return;
    }

    // Validate required fields
    if (!editingCostCurrency) {
      toast.error("Cost Currency is required");
      return;
    }

    if (!editingGrossCost || isNaN(grossCost!) || grossCost! < 0) {
      toast.error("Please enter a valid Gross Cost");
      return;
    }

    if (!editingNetCost || isNaN(netCost!) || netCost! < 0) {
      toast.error("Please enter a valid Net Cost");
      return;
    }

    if (!editingMarginRate || isNaN(marginRate!) || marginRate! < 0) {
      toast.error("Please enter a valid Margin Rate");
      return;
    }

    if (!editingSellingPriceCostCurrency || isNaN(sellingPriceCostCurrency!) || sellingPriceCostCurrency! < 0) {
      toast.error("Please enter a valid Selling Price (Cost Currency)");
      return;
    }

    try {
      const datesToUpdate = selectedDate 
        ? [selectedDate] 
        : Array.from(selectedDates).map(dateStr => new Date(dateStr));

      for (const date of datesToUpdate) {
        const dateKey = format(date, 'yyyy-MM-dd');
        const existingPricing = monthlyPricingInfo.get(dateKey);

        await updatePricingMutation.mutateAsync({
          variantId,
          date: dateKey,
          amount: convertToCents(price),
          currencyCode,
          cost,
          margin,
          // Include new pricing fields
          costCurrency,
          grossCost,
          commission,
          netCost,
          marginRate,
          sellingPriceCostCurrency,
          // Include price_list_id if this date already has pricing
          ...(existingPricing?.priceListId && { price_list_id: existingPricing.priceListId }),
        });
      }

      toast.success(`Updated pricing for ${datesToUpdate.length} date(s)`);
      
      // Clear selections
      setSelectedDate(null);
      setSelectedDates(new Set());
      setEditingPrice("");
      setEditingCost("");
      setEditingMargin("");
      setEditingCostCurrency("");
      setEditingGrossCost("");
      setEditingCommission("");
      setEditingNetCost("");
      setEditingMarginRate("");
      setEditingSellingPriceCostCurrency("");
      
      // Refetch data
      refetchPricingData();
      onRefetch?.();
    } catch (error) {
      console.error("Error updating pricing:", error);
      toast.error("Failed to update pricing");
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!pricingData) {
    return (
      <div className="flex items-center justify-center h-64">
        <Text>No pricing data available</Text>
      </div>
    );
  }

  // Handle case where no pricing data is available
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">
            Loading pricing data...
          </Text>
        </div>
      </div>
    );
  }

  if (!pricingData) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">
            No pricing data available for this variant.
          </Text>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with navigation */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <IconButton
            variant="transparent"
            onClick={handlePreviousMonth}
          >
            <ChevronLeft className="w-4 h-4" />
          </IconButton>

          <Heading level="h2" className="text-xl font-semibold">
            {format(currentDate, "MMMM yyyy")}
          </Heading>

          <IconButton
            variant="transparent"
            onClick={handleNextMonth}
          >
            <ChevronRight className="w-4 h-4" />
          </IconButton>

          <Button
            variant="secondary"
            size="small"
            onClick={handleToday}
          >
            Today
          </Button>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Calendar className="w-4 h-4 text-muted-foreground" />
            <Text className="text-sm text-muted-foreground">
              Click dates to edit prices
            </Text>
          </div>
        </div>
      </div>

      {/* Calendar Grid */}
      <div
        className={`bg-background border border-border rounded-lg overflow-hidden transition-all duration-300 select-none ${
          sidebarOpen ? 'mr-[360px]' : 'mr-0'
        }`}
        style={{ userSelect: 'none', WebkitUserSelect: 'none', MozUserSelect: 'none' }}
      >
        {/* Day headers */}
        <div className="grid grid-cols-7 border-b border-border">
          {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map((day) => (
            <div
              key={day}
              className="p-3 text-center text-sm font-medium text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800"
            >
              {day}
            </div>
          ))}
        </div>

        {/* Calendar days */}
        <div className="grid grid-cols-7">
          {calendarDays.map((date) => {
            const dateKey = format(date, "yyyy-MM-dd");
            const pricingInfo = monthlyPricingInfo.get(dateKey);
            console.log({pricingInfo})
            const isCurrentMonth = isSameMonth(date, currentDate);
            const isTodayDate = isToday(date);
            const isSelected = selectedDate && format(selectedDate, "yyyy-MM-dd") === dateKey;
            const isMultiSelected = selectedDates.has(dateKey);
            const isAvailable = pricingInfo?.isAvailable ?? false;

            return (
              <div
                key={dateKey}
                className={`
                  p-2 border-r border-b border-border min-h-[120px] cursor-pointer
                  hover:bg-muted/50 transition-colors relative group
                  ${!isCurrentMonth ? "bg-muted/20 text-muted-foreground" : ""}
                  ${isTodayDate ? "bg-blue-50 dark:bg-blue-950/20" : ""}
                  ${canEdit && isAvailable ? "hover:bg-blue-50 dark:hover:bg-blue-950/30" : ""}
                  ${pricingInfo?.isPriceListOverride && isAvailable ? "bg-orange-50 dark:bg-orange-950/20" : ""}
                  ${isSelected ? "bg-green-100 dark:bg-green-950/30 border-2 border-green-500" : ""}
                  ${isMultiSelected ? "bg-purple-100 dark:bg-purple-950/30 border-2 border-purple-500" : ""}
                  ${!isAvailable ? "bg-red-50 dark:bg-red-950/20 opacity-60" : ""}
                  ${!isAvailable ? "cursor-not-allowed" : ""}
                `}
                onClick={(e) => {
                  if (isAvailable) {
                    handleDateClick(date, e);
                  } else {
                    toast.error("Unavailable Date", {
                      description: "This variant is not available on this date. Pricing cannot be set for unavailable dates.",
                    });
                  }
                }}
                onMouseEnter={() => {
                  if (selectedDate && !isMultiSelectMode) {
                    setHoverDate(date);
                  }
                }}
                onMouseLeave={() => {
                  setHoverDate(null);
                }}
              >
                <div className="flex flex-col h-full">
                  <div className="flex items-center justify-between mb-1">
                    <span
                      className={`text-sm ${
                        isTodayDate ? "font-bold text-blue-600" : ""
                      }`}
                    >
                      {format(date, "d")}
                    </span>
                    <div className="flex items-center gap-1">
                      {isMultiSelected && (
                        <div className="w-4 h-4 bg-purple-500 rounded-full flex items-center justify-center">
                          <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                      {canEdit && !isMultiSelected && (
                        <Edit className="w-3 h-3 text-muted-foreground opacity-0 group-hover:opacity-100" />
                      )}
                    </div>
                  </div>

                  {/* Show pricing info only for available dates */}
                  {pricingInfo && isAvailable && isCurrentMonth && (
                    <div className="flex-1 space-y-1">
                      {/* Gross Cost - show if available */}
                      {canEdit && pricingData?.variant?.metadata?.gross_cost && (
                        <div className="text-xs">
                          <span className="text-muted-foreground">Gross: </span>
                          <span className="font-medium">
                            {(() => {
                              const costCurrency = pricingData.variant.metadata.cost_currency;
                              const currencyOption = currencyOptions.find((c: CurrencyOption) => c.value === costCurrency);
                              return currencyOption?.symbol || costCurrency || getCurrencySymbol();
                            })()}{(pricingData.variant.metadata.gross_cost).toFixed(2)}
                          </span>
                        </div>
                      )}

                      {/* Net Cost - show if available */}
                      {canEdit && pricingData?.variant?.metadata?.net_cost && (
                        <div className="text-xs">
                          <span className="text-muted-foreground">Net: </span>
                          <span className="font-medium">
                            {(() => {
                              const costCurrency = pricingData.variant.metadata.cost_currency;
                              const currencyOption = currencyOptions.find((c: CurrencyOption) => c.value === costCurrency);
                              return currencyOption?.symbol || costCurrency || getCurrencySymbol();
                            })()}{(pricingData.variant.metadata.net_cost).toFixed(2)}
                          </span>
                        </div>
                      )}

                      {/* Margin Rate - show if available */}
                      {canEdit && pricingData?.variant?.metadata?.margin_rate && (
                        <div className="text-xs">
                          <span className="text-muted-foreground">Margin: </span>
                          <span className="font-medium">
                            {(pricingData.variant.metadata.margin_rate).toFixed(1)}%
                          </span>
                        </div>
                      )}

                      {/* Selling Price */}
                      <div className="text-sm font-bold">
                        <span className="text-muted-foreground">Price: </span>
                        <span className="text-green-600 dark:text-green-400">
                          {getCurrencySymbol()}{formatCurrency(pricingInfo.selling_price)}
                        </span>
                      </div>

                      {/* Availability indicator */}
                      {canEdit && pricingInfo.availableQuantity !== undefined && (
                        <div className="text-xs text-blue-600 dark:text-blue-400">
                          {pricingInfo.availableQuantity} available
                        </div>
                      )}
                    </div>
                  )}

                  {/* Show unavailable message for unavailable dates */}
                  {pricingInfo && !isAvailable && isCurrentMonth && (
                    <div className="flex-1 flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-xs text-red-600 dark:text-red-400 font-medium">
                          Not Available
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          No inventory available
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Edit Price Sidebar */}
      {sidebarOpen && (
        <div className="fixed inset-0 z-50 pointer-events-none">
          {/* Backdrop */}
          <div
            className={`absolute inset-0 bg-black/20 ${(isMultiSelectMode && selectedDates.size > 0) ? 'pointer-events-none' : 'pointer-events-auto'}`}
            onClick={(isMultiSelectMode && selectedDates.size > 0) ? undefined : handleSidebarClose}
          />

          {/* Sidebar - positioned from the right */}
          <div className="absolute right-0 top-0 w-[360px] bg-white dark:bg-gray-900 shadow-xl h-full overflow-y-auto flex flex-col border-l border-border pointer-events-auto">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-border">
              <Heading level="h3" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {isMultiSelectMode && selectedDates.size > 0
                  ? `Edit Pricing for ${selectedDates.size} Selected Dates`
                  : selectedDate
                    ? `Edit Pricing for ${format(selectedDate, "MMM dd, yyyy")}`
                    : "Edit Pricing"
                }
              </Heading>
              <Button
                variant="transparent"
                onClick={handleSidebarForceClose}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            {/* Content */}
            <div className="flex-1 p-6 space-y-4">
              {/* Cost Currency */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Cost Currency <span className="text-red-500">*</span>
                </label>
                <Select
                  value={editingCostCurrency}
                  onValueChange={setEditingCostCurrency}
                  disabled={currenciesLoading}
                >
                  <Select.Trigger className="w-full">
                    <Select.Value placeholder={currenciesLoading ? "Loading currencies..." : "Select currency"} />
                  </Select.Trigger>
                  <Select.Content>
                    {currencyOptions.map((currency: CurrencyOption) => (
                      <Select.Item key={currency.value} value={currency.value}>
                        {currency.label}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </div>

              {/* Gross Cost */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Gross Cost <span className="text-red-500">*</span>
                </label>
                <Input
                  type="number"
                  step="0.01"
                  value={editingGrossCost}
                  onChange={(e) => setEditingGrossCost(e.target.value)}
                  placeholder="0.00"
                  className="w-full"
                />
              </div>

              {/* Commission (%) */}
              <div>
                <label className="block text-sm font-medium mb-2">Commission (%)</label>
                <Input
                  type="number"
                  step="0.01"
                  value={editingCommission}
                  onChange={(e) => setEditingCommission(e.target.value)}
                  placeholder="0"
                  className="w-full"
                />
              </div>

              {/* Net Cost */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Net Cost <span className="text-red-500">*</span>
                </label>
                <Input
                  type="number"
                  step="0.01"
                  value={editingNetCost}
                  onChange={(e) => setEditingNetCost(e.target.value)}
                  placeholder="0.00"
                  className="w-full"
                />
              </div>

              {/* Margin Rate (%) */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Margin Rate (%) <span className="text-red-500">*</span>
                </label>
                <Input
                  type="number"
                  step="0.01"
                  value={editingMarginRate}
                  onChange={(e) => setEditingMarginRate(e.target.value)}
                  placeholder="0"
                  className="w-full"
                />
              </div>

              {/* Selling Price (Cost Currency) */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Selling Price (Cost Currency) <span className="text-red-500">*</span>
                </label>
                <Input
                  type="number"
                  step="0.01"
                  value={editingSellingPriceCostCurrency}
                  onChange={(e) => setEditingSellingPriceCostCurrency(e.target.value)}
                  placeholder="0.00"
                  className="w-full"
                />
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  onClick={handlePriceUpdate}
                  disabled={updatePricingMutation.isPending}
                  className="flex-1"
                >
                  {updatePricingMutation.isPending ? "Updating..." : "Update Pricing"}
                </Button>
                <Button
                  variant="secondary"
                  onClick={handleSidebarClose}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Legend */}
      <div className={`flex items-center gap-6 text-sm mt-6 transition-all duration-300 ${
        sidebarOpen ? 'mr-[360px]' : 'mr-0'
      }`}>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-orange-200 border border-orange-300 rounded"></div>
          <span>Price List Override</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-green-200 border border-green-300 rounded"></div>
          <span>Available</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-red-200 border border-red-300 rounded"></div>
          <span>Unavailable</span>
        </div>
      </div>
    </div>
  );
};

export default VariantPricingCalendarView;
