import React, { useState, useEffect } from "react";
import {
  Heading,
  Text,
  Select,
  Button,
} from "@camped-ai/ui";
import { Calendar, ChevronLeft } from "lucide-react";
import { useVariantsForPricing } from "../hooks/use-variant-pricing-calendar";
import VariantPricingCalendarView from "./variant-pricing-calendar-view";

export interface ProductVariant {
  id: string;
  title: string;
  sku: string;
  product_id: string;
  product?: {
    id: string;
    title: string;
    handle: string;
  };
  metadata?: Record<string, any>;
  inventory_quantity?: number;
  manage_inventory?: boolean;
}

const VariantPricingManager: React.FC = () => {
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const currencyCode = "GBP"; // Fixed currency

  // Fetch variants for dropdown
  const { data: variants = [], isLoading } = useVariantsForPricing();

  // Get variant ID from URL query string
  const getVariantIdFromUrl = (): string | null => {
    if (typeof window === 'undefined') return null;
    const params = new URLSearchParams(window.location.search);
    return params.get('variant_id');
  };

  // Update URL query string
  const updateUrlWithVariantId = (variantId: string | null) => {
    if (typeof window === 'undefined') return;
    const url = new URL(window.location.href);
    if (variantId) {
      url.searchParams.set('variant_id', variantId);
    } else {
      url.searchParams.delete('variant_id');
    }
    window.history.replaceState({}, '', url.toString());
  };

  // Initialize selected variant from URL on mount
  useEffect(() => {
    const variantIdFromUrl = getVariantIdFromUrl();
    if (variantIdFromUrl && variants.length > 0) {
      const variant = variants.find((v: ProductVariant) => v.id === variantIdFromUrl);
      if (variant) {
        setSelectedVariant(variant);
      }
    }
  }, [variants]);

  // Handle variant selection
  const handleVariantSelect = (variantId: string) => {
    const variant = variants.find((v: ProductVariant) => v.id === variantId);
    if (variant) {
      setSelectedVariant(variant);
      updateUrlWithVariantId(variantId);
    }
  };

  // Handle back to selection
  const handleBackToSelection = () => {
    setSelectedVariant(null);
    updateUrlWithVariantId(null);
  };

  // Handle price update
  const handlePriceUpdate = async (
    date: Date,
    price: number,
    costMarginData?: {
      cost: number;
      margin: number;
    }
  ) => {
    console.log("Price update:", { date, price, costMarginData });
  };

  // Handle data refetch
  const handleRefetch = () => {
    console.log("Refetching pricing data");
  };

  return (
    <div className="space-y-6">
      {!selectedVariant ? (
        <>
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <Heading level="h1">Product Variant Pricing</Heading>
              <Text className="text-muted-foreground">
                Manage date-based pricing for product variants
              </Text>
            </div>
          </div>

          {/* Variant Selection Dropdown */}
          <div className="max-w-md">
            <label className="block text-sm font-medium mb-2">
              Select Product Variant
            </label>
            <Select
              value={selectedVariant?.id || ""}
              onValueChange={handleVariantSelect}
              disabled={isLoading}
            >
              <Select.Trigger className="w-full">
                <Select.Value placeholder={isLoading ? "Loading variants..." : "Choose a variant"} />
              </Select.Trigger>
              <Select.Content>
                {variants.map((variant: ProductVariant) => (
                  <Select.Item key={variant.id} value={variant.id}>
                    <div className="flex flex-col">
                      <span className="font-medium">{variant.title}</span>
                      <span className="text-sm text-muted-foreground">
                        {variant.product?.title} • SKU: {variant.sku}
                      </span>
                    </div>
                  </Select.Item>
                ))}
              </Select.Content>
            </Select>
          </div>
        </>
      ) : (
        <>
          {/* Header with back button */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="secondary"
                size="small"
                onClick={handleBackToSelection}
                className="flex items-center space-x-2"
              >
                <ChevronLeft className="w-4 h-4" />
                <span>Back to Selection</span>
              </Button>
              <div>
                <Heading level="h1" className="flex items-center space-x-2">
                  <Calendar className="w-6 h-6" />
                  <span>Variant Pricing Calendar</span>
                </Heading>
                <Text className="text-muted-foreground">
                  {selectedVariant.title} • {selectedVariant.product?.title} • SKU: {selectedVariant.sku}
                </Text>
              </div>
            </div>
          </div>

          {/* Calendar View */}
          <VariantPricingCalendarView
            variantId={selectedVariant.id}
            currencyCode={currencyCode}
            onPriceUpdate={handlePriceUpdate}
            onRefetch={handleRefetch}
            canEdit={true}
          />
        </>
      )}
    </div>
  );
};

export default VariantPricingManager;
