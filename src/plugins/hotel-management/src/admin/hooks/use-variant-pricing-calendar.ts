import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";

export interface VariantPricingData {
  variant: {
    id: string;
    title: string;
    sku: string;
    product_id: string;
    product_title: string;
    metadata: Record<string, any>;
  };
  base_pricing: Array<{
    id: string;
    amount: number;
    currency_code: string;
    variant_id: string;
  }>;
  date_based_pricing: Array<{
    date: string;
    price_list_id?: string;
    price_list_name?: string;
    amount: number;
    currency_code: string;
    is_base_price: boolean;
    cost?: number;
    margin?: number;
    selling_price?: number;
  }>;
  inventory_availability: Array<{
    id: string;
    from_date: string;
    to_date: string;
    available_quantity: number;
    status: string;
    inventory_item_id: string;
  }>;
  currency_code: string;
  date_range: {
    start_date?: string;
    end_date?: string;
  };
}

export interface UseVariantPricingCalendarParams {
  variantId: string;
  startDate: Date;
  endDate: Date;
  currencyCode?: string;
  enabled?: boolean;
}

/**
 * Hook to fetch variant pricing data for calendar view
 */
export const useVariantPricingCalendar = ({
  variantId,
  startDate,
  endDate,
  currencyCode = "GBP",
  enabled = true,
}: UseVariantPricingCalendarParams) => {
  const startDateStr = format(startDate, "yyyy-MM-dd");
  const endDateStr = format(endDate, "yyyy-MM-dd");

  return useQuery({
    queryKey: [
      "variant-pricing-calendar",
      variantId,
      startDateStr,
      endDateStr,
      currencyCode,
    ],
    queryFn: async (): Promise<VariantPricingData> => {
      const params = new URLSearchParams({
        start_date: startDateStr,
        end_date: endDateStr,
        currency_code: currencyCode,
      });

      const response = await fetch(
        `/admin/hotel-management/variants/${variantId}/pricing?${params}`,
        {
          credentials: 'include',
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch variant pricing: ${response.statusText}`);
      }

      return response.json();
    },
    enabled: enabled && !!variantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export interface UpdateVariantPricingParams {
  variantId: string;
  date: string;
  amount: number;
  currencyCode?: string;
  cost?: number;
  margin?: number;
  price_list_id?: string;
  // New pricing fields
  costCurrency?: string;
  grossCost?: number;
  commission?: number;
  netCost?: number;
  marginRate?: number;
  sellingPriceCostCurrency?: number;
}

/**
 * Hook to update variant pricing for specific dates
 */
export const useUpdateVariantPricing = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: UpdateVariantPricingParams) => {
      const { variantId, ...body } = params;

      const response = await fetch(
        `/admin/hotel-management/variants/${variantId}/pricing`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: 'include',
          body: JSON.stringify({
            date: body.date,
            amount: body.amount,
            currency_code: body.currencyCode || "GBP",
            cost: body.cost,
            margin: body.margin,
            price_list_id: body.price_list_id,
            // New pricing fields
            cost_currency: body.costCurrency,
            gross_cost: body.grossCost,
            commission: body.commission,
            net_cost: body.netCost,
            margin_rate: body.marginRate,
            selling_price_cost_currency: body.sellingPriceCostCurrency,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update variant pricing");
      }

      return response.json();
    },
    onSuccess: (_data, variables) => {
      // Invalidate and refetch variant pricing queries
      queryClient.invalidateQueries({
        queryKey: ["variant-pricing-calendar", variables.variantId],
      });
      
      // Also invalidate any related product queries
      queryClient.invalidateQueries({
        queryKey: ["products"],
      });
    },
  });
};

/**
 * Hook to get available product variants for pricing calendar
 */
export const useVariantsForPricing = (productId?: string) => {
  return useQuery({
    queryKey: ["supplier-products-services-for-pricing", productId],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (productId) {
        params.append("product_id", productId);
      }

      const response = await fetch(
        `/admin/supplier-management/products-services?${params}`,
        {
          credentials: 'include',
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch products/services");
      }

      const data = await response.json();

      // Log the data structure for debugging
      console.log("Supplier products-services API response:", data);

      // Transform the supplier product-services data to match variant structure
      const variants = [];

      // Handle the supplier product-services response structure
      const productServices = data.product_services || [];

      if (Array.isArray(productServices)) {
        for (const service of productServices) {
          // Transform each product service to variant structure
          variants.push({
            id: service.product_variant_id, // Use the variant ID from the service
            title: service.name,
            sku: service.id, // Use service ID as SKU
            product_id: service.product_id,
            product: {
              id: service.product_id,
              title: service.name,
              handle: service.id,
            },
            metadata: {
              cost: parseFloat(service.gross_cost || "0"),
              cost_currency: service.cost_currency,
              selling_margin: parseFloat(service.margin_rate || "0"),
              commission: parseFloat(service.commission || "0"),
              service_level: service.service_level,
              add_on_service: service.type === "Service",
              unit_type: service.unit_type?.name,
              category: service.category?.name,
              status: service.status,
              valid_from: service.valid_from,
              valid_to: service.valid_to,
            },
            manage_inventory: true,
            inventory_quantity: service.status === "active" ? 999 : 0,
          });
        }
      }

      console.log("Transformed variants:", variants);
      return variants;
    },
    enabled: true,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Helper function to get pricing for a specific date
 */
export const getPricingForDate = (
  date: Date,
  pricingData: VariantPricingData
): {
  amount: number;
  currency_code: string;
  is_base_price: boolean;
  cost?: number;
  margin?: number;
  price_list_id?: string;
  price_list_name?: string;
  selling_price?: number;
} | null => {
  const dateKey = format(date, 'yyyy-MM-dd');
  console.log(pricingData.date_based_pricing, dateKey );
  
  // Look for date-specific pricing first
  const datePricing = pricingData.date_based_pricing.find(
    p => p.date === dateKey && !p.is_base_price
  );
  console.log(JSON.stringify(datePricing))
  if (datePricing) {
    return datePricing;
  }
  
  
  return  null;
};

/**
 * Helper function to get availability for a specific date
 */
export const getAvailabilityForDate = (
  date: Date,
  inventoryData: VariantPricingData['inventory_availability']
): {
  available_quantity: number;
  status: string;
  is_available: boolean;
} => {
  const dateStr = format(date, 'yyyy-MM-dd');
  
  // Find inventory records that include this date
  const applicableInventory = inventoryData.filter(inv => {
    return dateStr >= inv.from_date && dateStr <= inv.to_date;
  });
  
  if (applicableInventory.length === 0) {
    return {
      available_quantity: 0,
      status: 'unavailable',
      is_available: false,
    };
  }
  
  // Sum up available quantities and check status
  const totalQuantity = applicableInventory.reduce(
    (sum, inv) => sum + inv.available_quantity, 
    0
  );
  
  const hasCapacity = applicableInventory.some(
    inv => inv.status === 'capacity' && inv.available_quantity > 0
  );
  
  return {
    available_quantity: totalQuantity,
    status: hasCapacity ? 'capacity' : applicableInventory[0].status,
    is_available: hasCapacity,
  };
};
