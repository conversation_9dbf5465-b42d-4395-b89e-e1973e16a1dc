import { format, parseISO, isWithinInterval } from "date-fns";

export interface VariantPricingContext {
  variantId: string;
  currencyCode: string;
}

export interface DailyVariantPricingInfo {
  date: Date;
  cost: number | null;
  margin: number | null;
  selling_price: number;
  isPriceListOverride: boolean;
  priceListName?: string;
  priceListId?: string;
  isAvailable: boolean;
  availableQuantity: number;
  inventoryStatus: string;
}

export interface VariantPriceRule {
  id: string;
  variant_id: string;
  amount: number;
  currency_code: string;
  price_list_id?: string;
  price_list_name?: string;
  starts_at?: string;
  ends_at?: string;
  is_base_price: boolean;
}

export interface VariantInventoryLevel {
  id: string;
  from_date: string;
  to_date: string;
  available_quantity: number;
  status: string;
  inventory_item_id: string;
}

/**
 * Calculate the price for a specific date and variant
 */
export function calculateVariantDailyPrice(
  date: Date,
  context: VariantPricingContext,
  priceRules: VariantPriceRule[]
): number {
  const dateStr = format(date, 'yyyy-MM-dd');
  
  // Find date-specific price rules first
  const dateSpecificRules = priceRules.filter(rule => {
    if (rule.is_base_price) return false;
    if (!rule.starts_at || !rule.ends_at) return false;
    
    const startDate = parseISO(rule.starts_at);
    const endDate = parseISO(rule.ends_at);
    
    return isWithinInterval(date, { start: startDate, end: endDate });
  });
  
  // Use the most specific rule (latest created or highest priority)
  if (dateSpecificRules.length > 0) {
    const rule = dateSpecificRules[0]; // Assuming they're sorted by priority
    return rule.amount;
  }
  
  // Fall back to base price
  const baseRule = priceRules.find(rule => 
    rule.is_base_price && 
    rule.variant_id === context.variantId &&
    rule.currency_code === context.currencyCode
  );
  
  return baseRule ? baseRule.amount : 0;
}

/**
 * Calculate comprehensive daily pricing information for a variant
 */
export function calculateVariantDailyPricingInfo(
  date: Date,
  context: VariantPricingContext,
  priceRules: VariantPriceRule[],
  inventoryLevels: VariantInventoryLevel[],
  variantMetadata?: Record<string, any>
): DailyVariantPricingInfo {
  const dateStr = format(date, 'yyyy-MM-dd');
  
  // Find applicable price rule
  const dateSpecificRules = priceRules.filter(rule => {
    if (rule.is_base_price) return false;
    if (!rule.starts_at || !rule.ends_at) return false;
    
    const startDate = parseISO(rule.starts_at);
    const endDate = parseISO(rule.ends_at);
    
    return isWithinInterval(date, { start: startDate, end: endDate });
  });
  
  let price = 0;
  let isPriceListOverride = false;
  let priceListName: string | undefined;
  
  if (dateSpecificRules.length > 0) {
    const rule = dateSpecificRules[0];
    price = rule.amount;
    isPriceListOverride = true;
    priceListName = rule.price_list_name;
  } else {
    // Use base price
    const baseRule = priceRules.find(rule => 
      rule.is_base_price && 
      rule.variant_id === context.variantId &&
      rule.currency_code === context.currencyCode
    );
    price = baseRule ? baseRule.amount : 0;
  }
  
  // Get cost and margin from variant metadata
  const cost = variantMetadata?.cost || null;
  const margin = variantMetadata?.selling_margin || null;
  
  // Calculate availability
  const applicableInventory = inventoryLevels.filter(inv => {
    return dateStr >= inv.from_date && dateStr <= inv.to_date;
  });
  
  let isAvailable = false;
  let availableQuantity = 0;
  let inventoryStatus = 'unavailable';
  
  if (applicableInventory.length > 0) {
    availableQuantity = applicableInventory.reduce(
      (sum, inv) => sum + inv.available_quantity, 
      0
    );
    
    const hasCapacity = applicableInventory.some(
      inv => inv.status === 'capacity' && inv.available_quantity > 0
    );
    
    isAvailable = hasCapacity;
    inventoryStatus = hasCapacity ? 'capacity' : applicableInventory[0].status;
  }
  
  return {
    date,
    cost,
    margin,
    selling_price: price,
    isPriceListOverride,
    priceListName,
    isAvailable,
    availableQuantity,
    inventoryStatus,
  };
}

/**
 * Calculate prices for a range of dates
 */
export function calculateVariantPricesForDateRange(
  startDate: Date,
  endDate: Date,
  context: VariantPricingContext,
  priceRules: VariantPriceRule[],
  inventoryLevels: VariantInventoryLevel[],
  variantMetadata?: Record<string, any>
): Map<string, DailyVariantPricingInfo> {
  const prices = new Map<string, DailyVariantPricingInfo>();
  
  const currentDate = new Date(startDate);
  while (currentDate <= endDate) {
    const dateKey = format(currentDate, 'yyyy-MM-dd');
    const pricingInfo = calculateVariantDailyPricingInfo(
      new Date(currentDate),
      context,
      priceRules,
      inventoryLevels,
      variantMetadata
    );
    prices.set(dateKey, pricingInfo);
    
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return prices;
}

/**
 * Format price for display
 */
export function formatVariantPrice(
  amount: number,
  currencyCode: string,
  locale: string = 'en-GB'
): string {
  // Convert from cents to currency units
  const displayAmount = amount / 100;
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(displayAmount);
}

/**
 * Convert currency units to cents for storage
 */
export function convertToCents(amount: number): number {
  return Math.round(amount * 100);
}

/**
 * Convert cents to currency units for display
 */
export function convertFromCents(amount: number): number {
  return amount / 100;
}

/**
 * Validate pricing data
 */
export function validateVariantPricingData(data: {
  date: string;
  amount: number;
  currencyCode: string;
  cost?: number;
  margin?: number;
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Validate date format
  try {
    parseISO(data.date);
  } catch {
    errors.push("Invalid date format. Use YYYY-MM-DD.");
  }
  
  // Validate amount
  if (typeof data.amount !== 'number' || data.amount < 0) {
    errors.push("Amount must be a positive number.");
  }
  
  // Validate currency code
  if (!data.currencyCode || data.currencyCode.length !== 3) {
    errors.push("Currency code must be a 3-letter ISO code.");
  }
  
  // Validate cost if provided
  if (data.cost !== undefined && (typeof data.cost !== 'number' || data.cost < 0)) {
    errors.push("Cost must be a positive number.");
  }
  
  // Validate margin if provided
  if (data.margin !== undefined && (typeof data.margin !== 'number' || data.margin < 0 || data.margin > 1)) {
    errors.push("Margin must be a number between 0 and 1.");
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Get currency symbol for display
 */
export function getCurrencySymbol(currencyCode: string): string {
  const symbols: Record<string, string> = {
    'GBP': '£',
    'USD': '$',
    'EUR': '€',
    'CHF': 'CHF',
    'JPY': '¥',
  };
  
  return symbols[currencyCode] || currencyCode;
}
