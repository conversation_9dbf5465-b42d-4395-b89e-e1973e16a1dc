import { RouteConfig } from "@camped-ai/admin-ui";
import { Calendar } from "lucide-react";
import { VariantPricingManager } from "../../../../plugins/hotel-management/src/admin/components";

const VariantPricingPage = () => {
  return (
    <div className="flex flex-col gap-y-2">
      <VariantPricingManager />
    </div>
  );
};

export const config: RouteConfig = {
  link: {
    label: "Variant Pricing",
    icon: Calendar,
  },
};

export default VariantPricingPage;
